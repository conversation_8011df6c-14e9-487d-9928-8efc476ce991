name: ObsidianWardenPlugin
version: '${project.version}'
main: com.example.obsidianwarden.ObsidianWardenPlugin
api-version: '1.21'
description: A custom boss entity plugin featuring The Obsidian Warden
author: PluginAuthor
website: https://example.com

commands:
  obsidianwarden:
    description: Spawn an Obsidian Warden
    usage: /obsidianwarden [spawn|give]
    permission: obsidianwarden.admin
    aliases: [ow, warden]

permissions:
  obsidianwarden.admin:
    description: Allows access to Obsidian Warden admin commands
    default: op
  obsidianwarden.spawn:
    description: Allows spawning Obsidian Wardens
    default: op
