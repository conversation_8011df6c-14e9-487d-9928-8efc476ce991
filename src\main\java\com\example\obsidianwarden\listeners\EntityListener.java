package com.example.obsidianwarden.listeners;

import com.example.obsidianwarden.entity.ObsidianWardenEntity;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.entity.WitherSkeleton;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;

/**
 * Event listener for Obsidian Warden related events.
 * 
 * Handles:
 * - Spawn item usage
 * - Preventing damage to model parts
 * - Entity cleanup on death
 * - Special combat mechanics
 */
public class EntityListener implements Listener {

    /**
     * Handle player interactions (spawn item usage)
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        // Check if player is using the Obsidian Warden spawn item
        if (item != null && item.getType() == Material.OBSIDIAN && 
            item.hasItemMeta() && item.getItemMeta().hasDisplayName() &&
            item.getItemMeta().getDisplayName().contains("Obsidian Warden Spawn Crystal")) {
            
            if (!player.hasPermission("obsidianwarden.spawn")) {
                player.sendMessage(ChatColor.RED + "You don't have permission to spawn Obsidian Wardens!");
                return;
            }
            
            // Spawn the Obsidian Warden
            ObsidianWardenEntity warden = new ObsidianWardenEntity(player.getLocation());
            
            player.sendMessage(ChatColor.GREEN + "Summoned an Obsidian Warden!");
            player.sendMessage(ChatColor.YELLOW + "Warden ID: " + warden.getWarderId());
            
            // Consume the item
            if (item.getAmount() > 1) {
                item.setAmount(item.getAmount() - 1);
            } else {
                player.getInventory().setItemInMainHand(null);
            }
            
            event.setCancelled(true);
        }
    }

    /**
     * Prevent damage to Obsidian Warden model parts (armor stands)
     * and redirect damage to the main entity
     */
    @EventHandler
    public void onEntityDamage(EntityDamageByEntityEvent event) {
        // Check if an armor stand that's part of an Obsidian Warden is being damaged
        if (event.getEntity() instanceof ArmorStand) {
            ArmorStand armorStand = (ArmorStand) event.getEntity();
            
            // Check if this armor stand is part of an Obsidian Warden
            if (armorStand.hasMetadata("obsidian_warden_id")) {
                // Cancel damage to the armor stand
                event.setCancelled(true);
                
                // Find the corresponding Obsidian Warden and damage it instead
                String wardenId = armorStand.getMetadata("obsidian_warden_id").get(0).asString();
                
                for (ObsidianWardenEntity warden : ObsidianWardenEntity.getActiveWardens()) {
                    if (warden.getWarderId().toString().equals(wardenId)) {
                        // Damage the main entity instead
                        warden.getMainEntity().damage(event.getDamage(), event.getDamager());
                        
                        // Give feedback to the attacker
                        if (event.getDamager() instanceof Player) {
                            Player attacker = (Player) event.getDamager();
                            String partName = armorStand.getMetadata("obsidian_warden_part").get(0).asString();
                            attacker.sendMessage(ChatColor.GRAY + "You hit the Obsidian Warden's " + partName.toLowerCase() + "!");
                        }
                        break;
                    }
                }
            }
        }
        
        // Handle special combat mechanics for the main entity
        if (event.getEntity() instanceof WitherSkeleton) {
            WitherSkeleton skeleton = (WitherSkeleton) event.getEntity();
            
            // Check if this is an Obsidian Warden main entity
            ObsidianWardenEntity warden = ObsidianWardenEntity.findByMainEntity(skeleton);
            if (warden != null) {
                // Add special combat effects
                handleObsidianWardenCombat(warden, event);
            }
        }
    }

    /**
     * Handle special combat mechanics for Obsidian Warden
     */
    private void handleObsidianWardenCombat(ObsidianWardenEntity warden, EntityDamageByEntityEvent event) {
        if (event.getDamager() instanceof Player) {
            Player attacker = (Player) event.getDamager();
            
            // Special message for attacking the Obsidian Warden
            attacker.sendMessage(ChatColor.DARK_PURPLE + "The Obsidian Warden's obsidian hide absorbs some of the damage!");
            
            // Reduce damage slightly (obsidian is tough!)
            event.setDamage(event.getDamage() * 0.8);
            
            // Add knockback resistance effect
            if (Math.random() < 0.3) { // 30% chance
                attacker.sendMessage(ChatColor.GRAY + "Your attack barely phases the massive creature!");
                event.setDamage(event.getDamage() * 0.5);
            }
        }
    }

    /**
     * Handle entity death events
     */
    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        // Check if an Obsidian Warden main entity died
        if (event.getEntity() instanceof WitherSkeleton) {
            WitherSkeleton skeleton = (WitherSkeleton) event.getEntity();
            
            ObsidianWardenEntity warden = ObsidianWardenEntity.findByMainEntity(skeleton);
            if (warden != null) {
                // Clear default drops
                event.getDrops().clear();
                event.setDroppedExp(100); // Bonus experience
                
                // Custom death message
                if (event.getEntity().getKiller() instanceof Player) {
                    Player killer = (Player) event.getEntity().getKiller();
                    killer.sendMessage(ChatColor.GOLD + "You have defeated the mighty Obsidian Warden!");
                    killer.sendMessage(ChatColor.YELLOW + "The ancient power within it has been released...");
                }
                
                // The warden's own death handling will take care of cleanup and drops
            }
        }
    }

    /**
     * Prevent armor stands from being broken by players
     */
    @EventHandler
    public void onArmorStandBreak(EntityDamageByEntityEvent event) {
        if (event.getEntity().getType() == EntityType.ARMOR_STAND) {
            ArmorStand armorStand = (ArmorStand) event.getEntity();
            
            // Protect Obsidian Warden parts from being broken
            if (armorStand.hasMetadata("obsidian_warden_id")) {
                event.setCancelled(true);
                
                if (event.getDamager() instanceof Player) {
                    Player player = (Player) event.getDamager();
                    player.sendMessage(ChatColor.RED + "You cannot break parts of the Obsidian Warden directly!");
                    player.sendMessage(ChatColor.YELLOW + "Attack the main body to damage it!");
                }
            }
        }
    }
}
