@echo off
echo Building Obsidian Warden Plugin...

REM Create directories
if not exist "target" mkdir target
if not exist "target\classes" mkdir target\classes
if not exist "lib" mkdir lib

REM Download Paper API if not exists
if not exist "lib\paper-api-1.21.1-R0.1-SNAPSHOT.jar" (
    echo Downloading Paper API...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo.papermc.io/repository/maven-public/io/papermc/paper/paper-api/1.21.1-R0.1-SNAPSHOT/paper-api-1.21.1-R0.1-20241203.101021-141.jar' -OutFile 'lib\paper-api-1.21.1-R0.1-SNAPSHOT.jar'"
)

REM Compile Java files
echo Compiling Java files...
javac -cp "lib\paper-api-1.21.1-R0.1-SNAPSHOT.jar" -d target\classes src\main\java\com\example\obsidianwarden\*.java src\main\java\com\example\obsidianwarden\entity\*.java src\main\java\com\example\obsidianwarden\commands\*.java src\main\java\com\example\obsidianwarden\listeners\*.java

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

REM Copy resources
echo Copying resources...
xcopy /E /I src\main\resources target\classes

REM Create JAR
echo Creating JAR file...
cd target\classes
jar cf ..\obsidian-warden-plugin-1.0.0.jar *
cd ..\..

echo Build complete! JAR file created at: target\obsidian-warden-plugin-1.0.0.jar
pause
