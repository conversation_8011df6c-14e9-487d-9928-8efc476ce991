package com.example.obsidianwarden.commands;

import com.example.obsidianwarden.entity.ObsidianWardenEntity;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.List;

/**
 * Command handler for Obsidian Warden related commands.
 * 
 * Commands:
 * - /obsidianwarden spawn - Spawn an Obsidian Warden at the player's location
 * - /obsidianwarden give - Give the player an Obsidian Warden spawn item
 * - /obsidianwarden list - List all active Obsidian Wardens
 * - /obsidianwarden cleanup - Remove all Obsidian Wardens
 */
public class ObsidianWardenCommand implements CommandExecutor, TabCompleter {

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        
        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "spawn":
                return handleSpawn(sender, args);
            case "give":
                return handleGive(sender, args);
            case "list":
                return handleList(sender);
            case "cleanup":
                return handleCleanup(sender);
            case "help":
                sendHelp(sender);
                return true;
            default:
                sender.sendMessage(ChatColor.RED + "Unknown subcommand: " + subCommand);
                sendHelp(sender);
                return true;
        }
    }

    /**
     * Handle the spawn subcommand
     */
    private boolean handleSpawn(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can spawn Obsidian Wardens!");
            return true;
        }

        if (!sender.hasPermission("obsidianwarden.spawn")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to spawn Obsidian Wardens!");
            return true;
        }

        Player player = (Player) sender;
        Location spawnLocation = player.getLocation();

        // Spawn the Obsidian Warden
        ObsidianWardenEntity warden = new ObsidianWardenEntity(spawnLocation);
        
        player.sendMessage(ChatColor.GREEN + "Spawned an Obsidian Warden!");
        player.sendMessage(ChatColor.YELLOW + "Warden ID: " + warden.getWarderId());
        
        return true;
    }

    /**
     * Handle the give subcommand
     */
    private boolean handleGive(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Only players can receive items!");
            return true;
        }

        if (!sender.hasPermission("obsidianwarden.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to get Obsidian Warden items!");
            return true;
        }

        Player player = (Player) sender;
        
        // Create a special spawn item
        ItemStack spawnItem = createObsidianWardenSpawnItem();
        player.getInventory().addItem(spawnItem);
        
        player.sendMessage(ChatColor.GREEN + "Given Obsidian Warden spawn item!");
        player.sendMessage(ChatColor.YELLOW + "Right-click to spawn an Obsidian Warden!");
        
        return true;
    }

    /**
     * Handle the list subcommand
     */
    private boolean handleList(CommandSender sender) {
        if (!sender.hasPermission("obsidianwarden.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to list Obsidian Wardens!");
            return true;
        }

        var activeWardens = ObsidianWardenEntity.getActiveWardens();
        
        if (activeWardens.isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + "No active Obsidian Wardens found.");
            return true;
        }

        sender.sendMessage(ChatColor.GREEN + "Active Obsidian Wardens (" + activeWardens.size() + "):");
        
        for (ObsidianWardenEntity warden : activeWardens) {
            Location loc = warden.getMainEntity().getLocation();
            String locationStr = String.format("%.1f, %.1f, %.1f in %s", 
                loc.getX(), loc.getY(), loc.getZ(), loc.getWorld().getName());
            
            double health = warden.getMainEntity().getHealth();
            double maxHealth = warden.getMainEntity().getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH).getValue();
            
            sender.sendMessage(ChatColor.GRAY + "- " + ChatColor.WHITE + warden.getWarderId() + 
                ChatColor.GRAY + " at " + ChatColor.YELLOW + locationStr + 
                ChatColor.GRAY + " (" + ChatColor.RED + String.format("%.1f/%.1f HP", health, maxHealth) + ChatColor.GRAY + ")");
        }
        
        return true;
    }

    /**
     * Handle the cleanup subcommand
     */
    private boolean handleCleanup(CommandSender sender) {
        if (!sender.hasPermission("obsidianwarden.admin")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to cleanup Obsidian Wardens!");
            return true;
        }

        int count = ObsidianWardenEntity.getActiveWardens().size();
        ObsidianWardenEntity.cleanupAll();
        
        sender.sendMessage(ChatColor.GREEN + "Cleaned up " + count + " Obsidian Warden(s)!");
        
        return true;
    }

    /**
     * Send help message
     */
    private void sendHelp(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== Obsidian Warden Commands ===");
        sender.sendMessage(ChatColor.YELLOW + "/obsidianwarden spawn" + ChatColor.WHITE + " - Spawn an Obsidian Warden");
        sender.sendMessage(ChatColor.YELLOW + "/obsidianwarden give" + ChatColor.WHITE + " - Get a spawn item");
        sender.sendMessage(ChatColor.YELLOW + "/obsidianwarden list" + ChatColor.WHITE + " - List active wardens");
        sender.sendMessage(ChatColor.YELLOW + "/obsidianwarden cleanup" + ChatColor.WHITE + " - Remove all wardens");
        sender.sendMessage(ChatColor.YELLOW + "/obsidianwarden help" + ChatColor.WHITE + " - Show this help");
    }

    /**
     * Create a special spawn item for the Obsidian Warden
     */
    private ItemStack createObsidianWardenSpawnItem() {
        ItemStack item = new ItemStack(Material.OBSIDIAN);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName(ChatColor.DARK_PURPLE + "Obsidian Warden Spawn Crystal");
        meta.setLore(Arrays.asList(
            ChatColor.GRAY + "A mysterious crystal that summons",
            ChatColor.GRAY + "the mighty Obsidian Warden.",
            "",
            ChatColor.YELLOW + "Right-click to spawn!"
        ));
        
        item.setItemMeta(meta);
        return item;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (args.length == 1) {
            return Arrays.asList("spawn", "give", "list", "cleanup", "help");
        }
        return null;
    }
}
