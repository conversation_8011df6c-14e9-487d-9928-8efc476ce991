package com.example.obsidianwarden.entity;

import com.example.obsidianwarden.ObsidianWardenPlugin;
import org.bukkit.*;
import org.bukkit.attribute.Attribute;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.entity.*;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.EulerAngle;
import org.bukkit.util.Vector;

import java.util.*;

/**
 * The Obsidian Warden - a custom boss entity created using vanilla Minecraft entities.
 * 
 * This class creates a "composite entity" made of:
 * - Main body: Wither Skeleton (for AI, health, and combat)
 * - Arms: 2 Armor Stands with nether brick blocks
 * - Head decorations: 2 Armor Stands with crying obsidian (horns)
 * - Floating shards: 3 Armor Stands with purple glass
 * - Belt: 1 Armor Stand with end stone
 * 
 * All parts move together and animate to create the illusion of a single custom entity.
 */
public class ObsidianWardenEntity {
    
    // Static tracking of all active Obsidian Wardens
    private static final Set<ObsidianWardenEntity> activeWardens = new HashSet<>();
    private static BukkitRunnable updateTask;
    
    // Main entity components
    private final WitherSkeleton mainEntity;
    private final BossBar bossBar;
    private final UUID warderId;
    
    // Model parts (armor stands)
    private final ArmorStand leftArm;
    private final ArmorStand rightArm;
    private final ArmorStand leftHorn;
    private final ArmorStand rightHorn;
    private final ArmorStand belt;
    private final ArmorStand shard1;
    private final ArmorStand shard2;
    private final ArmorStand shard3;
    
    // Animation state
    private int animationTicks = 0;
    private boolean isDead = false;

    /**
     * Create a new Obsidian Warden at the specified location
     */
    public ObsidianWardenEntity(Location location) {
        this.warderId = UUID.randomUUID();
        
        // Create the main entity (Wither Skeleton for AI and combat)
        this.mainEntity = (WitherSkeleton) location.getWorld().spawnEntity(location, EntityType.WITHER_SKELETON);
        setupMainEntity();
        
        // Create the boss bar
        this.bossBar = Bukkit.createBossBar("Obsidian Warden", BarColor.PURPLE, BarStyle.SOLID);
        
        // Create all the model parts
        this.leftArm = createModelPart(location, "Left Arm", createNetherBrickBlock());
        this.rightArm = createModelPart(location, "Right Arm", createNetherBrickBlock());
        this.leftHorn = createModelPart(location, "Left Horn", createCryingObsidianBlock());
        this.rightHorn = createModelPart(location, "Right Horn", createCryingObsidianBlock());
        this.belt = createModelPart(location, "Belt", createEndStoneBlock());
        this.shard1 = createModelPart(location, "Shard 1", createPurpleGlassBlock());
        this.shard2 = createModelPart(location, "Shard 2", createPurpleGlassBlock());
        this.shard3 = createModelPart(location, "Shard 3", createPurpleGlassBlock());
        
        // Add to tracking
        activeWardens.add(this);
        
        ObsidianWardenPlugin.getInstance().getLogger().info("Created Obsidian Warden with ID: " + warderId);
    }

    /**
     * Set up the main entity with boss-level stats and appearance
     */
    private void setupMainEntity() {
        // Set custom name
        mainEntity.setCustomName(ChatColor.DARK_PURPLE + "Obsidian Warden");
        mainEntity.setCustomNameVisible(true);
        
        // Set boss-level attributes
        mainEntity.getAttribute(Attribute.GENERIC_MAX_HEALTH).setBaseValue(300.0);
        mainEntity.setHealth(300.0);
        mainEntity.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED).setBaseValue(0.25);
        mainEntity.getAttribute(Attribute.GENERIC_ATTACK_DAMAGE).setBaseValue(12.0);
        
        // Give obsidian armor for the "obsidian body" appearance
        mainEntity.getEquipment().setHelmet(createObsidianBlock());
        mainEntity.getEquipment().setChestplate(new ItemStack(Material.NETHERITE_CHESTPLATE));
        mainEntity.getEquipment().setLeggings(new ItemStack(Material.NETHERITE_LEGGINGS));
        mainEntity.getEquipment().setBoots(new ItemStack(Material.NETHERITE_BOOTS));
        
        // Weapon
        mainEntity.getEquipment().setItemInMainHand(new ItemStack(Material.NETHERITE_SWORD));
        
        // Prevent equipment drops
        mainEntity.getEquipment().setHelmetDropChance(0.0f);
        mainEntity.getEquipment().setChestplateDropChance(0.0f);
        mainEntity.getEquipment().setLeggingsDropChance(0.0f);
        mainEntity.getEquipment().setBootsDropChance(0.0f);
        mainEntity.getEquipment().setItemInMainHandDropChance(0.0f);
        
        // Add some boss effects
        mainEntity.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, Integer.MAX_VALUE, 0, false, false));
        mainEntity.addPotionEffect(new PotionEffect(PotionEffectType.KNOCKBACK_RESISTANCE, Integer.MAX_VALUE, 3, false, false));
    }

    /**
     * Create a model part (armor stand) with the specified item as a helmet
     */
    private ArmorStand createModelPart(Location baseLocation, String name, ItemStack item) {
        ArmorStand armorStand = (ArmorStand) baseLocation.getWorld().spawnEntity(baseLocation, EntityType.ARMOR_STAND);
        
        // Configure the armor stand
        armorStand.setVisible(false); // Invisible armor stand, only the item shows
        armorStand.setGravity(false); // Floating
        armorStand.setCanPickupItems(false);
        armorStand.setCustomName(ChatColor.GRAY + name);
        armorStand.setCustomNameVisible(false);
        armorStand.setInvulnerable(true); // Can't be damaged
        armorStand.setSilent(true); // No sounds
        
        // Set the item as helmet (this is what creates the visual)
        armorStand.getEquipment().setHelmet(item);
        armorStand.getEquipment().setHelmetDropChance(0.0f);
        
        // Add metadata to identify this as part of our warden
        armorStand.setMetadata("obsidian_warden_id", new org.bukkit.metadata.FixedMetadataValue(ObsidianWardenPlugin.getInstance(), warderId.toString()));
        armorStand.setMetadata("obsidian_warden_part", new org.bukkit.metadata.FixedMetadataValue(ObsidianWardenPlugin.getInstance(), name));
        
        return armorStand;
    }

    /**
     * Create item stacks for different model parts using vanilla blocks
     */
    private ItemStack createObsidianBlock() {
        ItemStack item = new ItemStack(Material.OBSIDIAN);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(ChatColor.DARK_PURPLE + "Obsidian Body");
        item.setItemMeta(meta);
        return item;
    }

    private ItemStack createNetherBrickBlock() {
        ItemStack item = new ItemStack(Material.NETHER_BRICKS);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(ChatColor.DARK_RED + "Nether Brick Arm");
        item.setItemMeta(meta);
        return item;
    }

    private ItemStack createCryingObsidianBlock() {
        ItemStack item = new ItemStack(Material.CRYING_OBSIDIAN);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(ChatColor.DARK_PURPLE + "Crying Obsidian Horn");
        item.setItemMeta(meta);
        return item;
    }

    private ItemStack createEndStoneBlock() {
        ItemStack item = new ItemStack(Material.END_STONE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(ChatColor.YELLOW + "End Stone Belt");
        item.setItemMeta(meta);
        return item;
    }

    private ItemStack createPurpleGlassBlock() {
        ItemStack item = new ItemStack(Material.PURPLE_STAINED_GLASS);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(ChatColor.LIGHT_PURPLE + "Floating Shard");
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Update the entity - called every tick for animations and positioning
     */
    public void update() {
        if (isDead || !mainEntity.isValid()) {
            cleanup();
            return;
        }

        animationTicks++;

        // Update boss bar
        updateBossBar();

        // Update model part positions and animations
        updateModelParts();

        // Check if entity should die
        if (mainEntity.getHealth() <= 0) {
            onDeath();
        }
    }

    /**
     * Update the boss bar health and visibility
     */
    private void updateBossBar() {
        double healthPercent = mainEntity.getHealth() / mainEntity.getAttribute(Attribute.GENERIC_MAX_HEALTH).getValue();
        bossBar.setProgress(Math.max(0.0, Math.min(1.0, healthPercent)));

        // Add nearby players to boss bar
        Location loc = mainEntity.getLocation();
        for (Player player : loc.getWorld().getPlayers()) {
            if (player.getLocation().distance(loc) <= 50) {
                bossBar.addPlayer(player);
            } else {
                bossBar.removePlayer(player);
            }
        }
    }

    /**
     * Update positions and animations of all model parts
     */
    private void updateModelParts() {
        Location mainLoc = mainEntity.getLocation();
        Vector direction = mainLoc.getDirection();

        // Calculate animation values
        double armSwayAmount = 0.3;
        double shardBobAmount = 0.2;
        double time = animationTicks * 0.05;

        // Arm sway animation - arms move opposite to each other
        double leftArmSway = Math.sin(time) * armSwayAmount;
        double rightArmSway = Math.sin(time + Math.PI) * armSwayAmount;

        // Shard floating animation - each with different timing
        double shard1Bob = Math.sin(time * 1.2) * shardBobAmount;
        double shard2Bob = Math.sin(time * 1.2 + 1.0) * shardBobAmount;
        double shard3Bob = Math.sin(time * 1.2 + 2.0) * shardBobAmount;

        // Position arms (left and right of main entity)
        Vector leftOffset = new Vector(-1.2, 0.5 + leftArmSway, 0).rotateAroundY(Math.toRadians(mainLoc.getYaw()));
        Vector rightOffset = new Vector(1.2, 0.5 + rightArmSway, 0).rotateAroundY(Math.toRadians(mainLoc.getYaw()));

        leftArm.teleport(mainLoc.clone().add(leftOffset));
        rightArm.teleport(mainLoc.clone().add(rightOffset));

        // Position horns (on head)
        Vector leftHornOffset = new Vector(-0.3, 2.2, 0).rotateAroundY(Math.toRadians(mainLoc.getYaw()));
        Vector rightHornOffset = new Vector(0.3, 2.2, 0).rotateAroundY(Math.toRadians(mainLoc.getYaw()));

        leftHorn.teleport(mainLoc.clone().add(leftHornOffset));
        rightHorn.teleport(mainLoc.clone().add(rightHornOffset));

        // Position belt (around waist)
        Vector beltOffset = new Vector(0, 0.8, 0);
        belt.teleport(mainLoc.clone().add(beltOffset));

        // Position floating shards (behind the entity)
        Vector backDirection = direction.clone().multiply(-1.5); // Behind the entity

        Vector shard1Offset = backDirection.clone().add(new Vector(-0.6, 1.5 + shard1Bob, 0));
        Vector shard2Offset = backDirection.clone().add(new Vector(0, 1.8 + shard2Bob, 0));
        Vector shard3Offset = backDirection.clone().add(new Vector(0.6, 1.3 + shard3Bob, 0));

        shard1.teleport(mainLoc.clone().add(shard1Offset));
        shard2.teleport(mainLoc.clone().add(shard2Offset));
        shard3.teleport(mainLoc.clone().add(shard3Offset));

        // Add rotation animations to shards for extra effect
        shard1.setHeadPose(new EulerAngle(0, time * 0.5, 0));
        shard2.setHeadPose(new EulerAngle(0, time * 0.7, 0));
        shard3.setHeadPose(new EulerAngle(0, time * 0.3, 0));
    }

    /**
     * Handle entity death
     */
    private void onDeath() {
        if (isDead) return;
        isDead = true;

        // Death effects
        Location loc = mainEntity.getLocation();
        loc.getWorld().spawnParticle(Particle.EXPLOSION_LARGE, loc, 5);
        loc.getWorld().playSound(loc, Sound.ENTITY_WITHER_DEATH, 1.0f, 0.8f);

        // Drop rewards
        loc.getWorld().dropItem(loc, new ItemStack(Material.OBSIDIAN, 8));
        loc.getWorld().dropItem(loc, new ItemStack(Material.CRYING_OBSIDIAN, 4));
        loc.getWorld().dropItem(loc, new ItemStack(Material.NETHER_STAR, 1));

        // Cleanup after a short delay for death animation
        new BukkitRunnable() {
            @Override
            public void run() {
                cleanup();
            }
        }.runTaskLater(ObsidianWardenPlugin.getInstance(), 40L); // 2 seconds
    }

    /**
     * Clean up this Obsidian Warden and all its parts
     */
    public void cleanup() {
        if (isDead) return;
        isDead = true;

        // Remove from tracking
        activeWardens.remove(this);

        // Remove boss bar
        bossBar.removeAll();

        // Remove all entities
        if (mainEntity.isValid()) mainEntity.remove();
        if (leftArm.isValid()) leftArm.remove();
        if (rightArm.isValid()) rightArm.remove();
        if (leftHorn.isValid()) leftHorn.remove();
        if (rightHorn.isValid()) rightHorn.remove();
        if (belt.isValid()) belt.remove();
        if (shard1.isValid()) shard1.remove();
        if (shard2.isValid()) shard2.remove();
        if (shard3.isValid()) shard3.remove();

        ObsidianWardenPlugin.getInstance().getLogger().info("Cleaned up Obsidian Warden: " + warderId);
    }

    // Getters
    public WitherSkeleton getMainEntity() { return mainEntity; }
    public UUID getWarderId() { return warderId; }
    public boolean isDead() { return isDead; }

    /**
     * Static methods for managing all Obsidian Wardens
     */

    /**
     * Start the update task that animates all active wardens
     */
    public static void startUpdateTask(ObsidianWardenPlugin plugin) {
        if (updateTask != null) {
            updateTask.cancel();
        }

        updateTask = new BukkitRunnable() {
            @Override
            public void run() {
                // Update all active wardens
                for (ObsidianWardenEntity warden : new HashSet<>(activeWardens)) {
                    warden.update();
                }
            }
        };

        // Run every tick (20 times per second) for smooth animations
        updateTask.runTaskTimer(plugin, 0L, 1L);
    }

    /**
     * Clean up all active Obsidian Wardens
     */
    public static void cleanupAll() {
        for (ObsidianWardenEntity warden : new HashSet<>(activeWardens)) {
            warden.cleanup();
        }
        activeWardens.clear();

        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
        }
    }

    /**
     * Get all active Obsidian Wardens
     */
    public static Set<ObsidianWardenEntity> getActiveWardens() {
        return new HashSet<>(activeWardens);
    }

    /**
     * Find an Obsidian Warden by its main entity
     */
    public static ObsidianWardenEntity findByMainEntity(Entity entity) {
        for (ObsidianWardenEntity warden : activeWardens) {
            if (warden.mainEntity.equals(entity)) {
                return warden;
            }
        }
        return null;
    }
}
