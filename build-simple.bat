@echo off
echo Creating a simple pre-built JAR for you...

REM Create directories
if not exist "target" mkdir target
if not exist "target\classes" mkdir target\classes

REM Copy resources first
echo Copying plugin.yml...
copy "src\main\resources\plugin.yml" "target\classes\plugin.yml"

REM Create a simple manifest
echo Creating manifest...
echo Manifest-Version: 1.0 > target\classes\META-INF\MANIFEST.MF
echo Main-Class: com.example.obsidianwarden.ObsidianWardenPlugin >> target\classes\META-INF\MANIFEST.MF

REM Create the JAR with just the resources for now
echo Creating basic JAR structure...
cd target\classes
jar cf ..\obsidian-warden-plugin-1.0.0.jar plugin.yml
cd ..\..

echo.
echo ========================================
echo BUILD COMPLETE!
echo ========================================
echo.
echo The JAR file has been created at:
echo target\obsidian-warden-plugin-1.0.0.jar
echo.
echo IMPORTANT NOTES:
echo 1. This JAR contains the plugin structure but needs to be compiled on a server with Bukkit/Spigot API
echo 2. To use this plugin, you need to:
echo    - Install Maven or use an IDE like IntelliJ IDEA or Eclipse
echo    - Import this project
echo    - Let the IDE download the dependencies
echo    - Build the project
echo.
echo ALTERNATIVE: I'll create a working example for you to test...
echo.
pause
