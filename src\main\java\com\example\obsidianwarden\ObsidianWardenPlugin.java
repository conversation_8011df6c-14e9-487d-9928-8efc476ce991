package com.example.obsidianwarden;

import com.example.obsidianwarden.commands.ObsidianWardenCommand;
import com.example.obsidianwarden.entity.ObsidianWardenEntity;
import com.example.obsidianwarden.listeners.EntityListener;
import org.bukkit.plugin.java.JavaPlugin;

/**
 * Main plugin class for the Obsidian Warden Plugin.
 * 
 * This plugin creates a custom boss entity using vanilla Minecraft mechanics
 * and server-side entity manipulation. The "model" is created through:
 * 
 * 1. A main Wither Skeleton entity (for the body and AI)
 * 2. Armor stands positioned as "model parts" (arms, head decorations, floating shards)
 * 3. Custom equipment and effects to achieve the obsidian/nether brick appearance
 * 4. Synchronized movement and animations
 * 
 * This approach works on any server without requiring client-side mods!
 */
public final class ObsidianWardenPlugin extends JavaPlugin {

    private static ObsidianWardenPlugin instance;

    @Override
    public void onEnable() {
        instance = this;
        
        getLogger().info("Enabling Obsidian Warden Plugin...");
        
        // Register commands
        getCommand("obsidianwarden").setExecutor(new ObsidianWardenCommand());
        
        // Register event listeners
        getServer().getPluginManager().registerEvents(new EntityListener(), this);
        
        // Start the entity update task (for animations and synchronization)
        ObsidianWardenEntity.startUpdateTask(this);
        
        getLogger().info("Obsidian Warden Plugin enabled successfully!");
        getLogger().info("Use /obsidianwarden spawn to create an Obsidian Warden");
    }

    @Override
    public void onDisable() {
        getLogger().info("Disabling Obsidian Warden Plugin...");
        
        // Clean up any existing Obsidian Wardens
        ObsidianWardenEntity.cleanupAll();
        
        getLogger().info("Obsidian Warden Plugin disabled.");
    }

    /**
     * Get the plugin instance for static access
     */
    public static ObsidianWardenPlugin getInstance() {
        return instance;
    }
}
