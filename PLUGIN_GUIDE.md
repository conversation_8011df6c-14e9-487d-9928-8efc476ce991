# Obsidian Warden Plugin - Quick Start Guide

## Installation

1. **Download/Build the Plugin**
   ```bash
   mvn clean package
   ```
   This creates `target/obsidian-warden-plugin-1.0.0.jar`

2. **Install on Server**
   - Copy the JAR to your server's `plugins/` folder
   - Restart the server
   - Plugin will auto-enable

## Basic Usage

### Commands
```bash
# Spawn an Obsidian Warden at your location
/obsidianwarden spawn

# Get a spawn crystal item
/obsidianwarden give

# List all active wardens
/obsidianwarden list

# Remove all wardens
/obsidianwarden cleanup
```

### Permissions
Add to your permissions plugin:
```yaml
permissions:
  obsidianwarden.spawn: true    # Allow spawning
  obsidianwarden.admin: true    # Full admin access
```

## What Players See

When you spawn an Obsidian Warden, players will see:

1. **Main Entity**: A large Wither Skeleton with obsidian helmet and netherite armor
2. **Floating Arms**: Nether brick blocks that sway gently beside the main body
3. **Horn Decorations**: Crying obsidian blocks on the head
4. **Floating Shards**: Purple glass blocks that bob behind the entity
5. **Belt**: An end stone block around the waist
6. **Boss Health Bar**: Purple boss bar showing "Obsidian Warden"

## Technical Details

### Entity Composition
- **1 Wither Skeleton** (main entity with AI, health, combat)
- **8 Armor Stands** (visual model parts)
- **1 Boss Bar** (health display)
- **Synchronized Movement** (all parts move together)

### Performance
- **Minimal Impact**: Uses vanilla entities and simple math
- **Efficient Updates**: 20 TPS update loop for smooth animations
- **Auto Cleanup**: Removes all parts when entity dies

### Compatibility
- **Server Types**: Bukkit, Spigot, Paper
- **Minecraft Versions**: 1.21+ (easily adaptable to older versions)
- **Client Requirements**: None - works with vanilla clients
- **Plugin Conflicts**: Minimal - uses standard Bukkit API

## Customization Examples

### Change Entity Stats
```java
// In setupMainEntity() method
mainEntity.getAttribute(Attribute.GENERIC_MAX_HEALTH).setBaseValue(500.0); // More health
mainEntity.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED).setBaseValue(0.35); // Faster
```

### Add New Model Parts
```java
// In constructor
this.extraPart = createModelPart(location, "Extra Part", new ItemStack(Material.DIAMOND_BLOCK));

// In updateModelParts()
Vector extraOffset = new Vector(0, 3.0, 0); // Above the head
extraPart.teleport(mainLoc.clone().add(extraOffset));
```

### Modify Animations
```java
// In updateModelParts() - faster arm movement
double leftArmSway = Math.sin(time * 2.0) * armSwayAmount; // 2x speed

// Different animation pattern
double spiralMotion = Math.sin(time) * Math.cos(time * 0.5) * shardBobAmount;
```

### Change Block Types
```java
// Use different blocks for parts
private ItemStack createCustomBlock() {
    return new ItemStack(Material.EMERALD_BLOCK); // Emerald instead of obsidian
}
```

## Troubleshooting

### Common Issues

**"Entity parts don't move together"**
- Check that the update task is running
- Verify all armor stands have the correct metadata
- Ensure no other plugins are interfering with entity movement

**"Boss bar doesn't show"**
- Check player permissions
- Verify players are within 50 blocks
- Make sure the boss bar isn't being removed by other plugins

**"Parts disappear"**
- Armor stands might be getting removed by world cleanup
- Check that `setInvulnerable(true)` is being called
- Verify the update loop is still running

### Debug Commands
```bash
# Check active wardens
/obsidianwarden list

# Force cleanup if something goes wrong
/obsidianwarden cleanup

# Check console for error messages
tail -f logs/latest.log | grep ObsidianWarden
```

## Advanced Features

### Custom Drops
Modify the `onDeath()` method to add custom rewards:
```java
// In onDeath() method
loc.getWorld().dropItem(loc, new ItemStack(Material.NETHERITE_INGOT, 3));
loc.getWorld().dropItem(loc, createCustomRewardItem());
```

### Special Attacks
Add to the update loop:
```java
// In update() method
if (animationTicks % 100 == 0) { // Every 5 seconds
    performSpecialAttack();
}
```

### Particle Effects
```java
// In updateModelParts()
shard1.getLocation().getWorld().spawnParticle(
    Particle.PORTAL, 
    shard1.getLocation(), 
    5, 0.1, 0.1, 0.1, 0.02
);
```

This plugin demonstrates how to create impressive custom entities using only vanilla Minecraft mechanics and server-side code!
