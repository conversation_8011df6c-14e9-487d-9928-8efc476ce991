# Complete Tutorial: Creating The Obsidian Warden Plugin

This tutorial explains every step of creating a custom Minecraft boss entity plugin that works on any server without client mods.

## Step 1: Understanding the Plugin Approach

### Why Use a Plugin Instead of a Mod?
- **Server Compatibility** - Works on any Bukkit/Spigot/Paper server
- **No Client Requirements** - Players don't need to install anything
- **Universal Access** - Anyone can join and see the custom entity
- **Easier Distribution** - Just drop the JAR in the plugins folder

### Core Components
1. **Main Plugin Class** (`ObsidianWardenPlugin.java`) - Plugin initialization and management
2. **Composite Entity** (`ObsidianWardenEntity.java`) - Combines multiple vanilla entities
3. **Command Handler** (`ObsidianWardenCommand.java`) - Player commands for spawning
4. **Event Listener** (`EntityListener.java`) - Handles interactions and damage

## Step 2: The Composite Entity Concept

### Traditional Approach vs Plugin Approach
```java
// Traditional mod approach (requires client mods)
CustomEntity entity = new CustomEntity(world, location);
entity.setCustomModel(customModel);

// Plugin approach (works with vanilla clients)
WitherSkeleton mainEntity = spawnWitherSkeleton(location);  // AI and combat
ArmorStand leftArm = spawnArmorStand(location);             // Visual component
ArmorStand rightArm = spawnArmorStand(location);            // Visual component
// Synchronize all parts to move together
```

### Entity Setup and Stats
```java
// Set boss-level attributes on the main entity
mainEntity.getAttribute(Attribute.GENERIC_MAX_HEALTH).setBaseValue(300.0);
mainEntity.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED).setBaseValue(0.25);
mainEntity.getAttribute(Attribute.GENERIC_ATTACK_DAMAGE).setBaseValue(12.0);

// Give it obsidian armor for the "obsidian body" appearance
mainEntity.getEquipment().setHelmet(new ItemStack(Material.OBSIDIAN));
mainEntity.getEquipment().setChestplate(new ItemStack(Material.NETHERITE_CHESTPLATE));
```

**Key Learning Points:**
- Use vanilla entities as the base for AI and combat
- Armor stands serve as "model parts" with block items
- Equipment and effects create the visual theme

## Step 3: Creating Model Parts with Armor Stands

### Model Hierarchy (Plugin Version)
```
Main Entity (Wither Skeleton)
├── Body: Equipment on main entity
├── Left Arm: ArmorStand + Nether Brick Block
├── Right Arm: ArmorStand + Nether Brick Block
├── Left Horn: ArmorStand + Crying Obsidian Block
├── Right Horn: ArmorStand + Crying Obsidian Block
├── Belt: ArmorStand + End Stone Block
├── Shard 1: ArmorStand + Purple Glass Block
├── Shard 2: ArmorStand + Purple Glass Block
└── Shard 3: ArmorStand + Purple Glass Block
```

### Creating Model Parts
```java
// Create an armor stand "model part"
private ArmorStand createModelPart(Location baseLocation, String name, ItemStack item) {
    ArmorStand armorStand = (ArmorStand) baseLocation.getWorld().spawnEntity(baseLocation, EntityType.ARMOR_STAND);

    // Configure the armor stand
    armorStand.setVisible(false);        // Invisible armor stand, only the item shows
    armorStand.setGravity(false);        // Floating
    armorStand.setInvulnerable(true);    // Can't be damaged

    // Set the item as helmet (this creates the visual)
    armorStand.getEquipment().setHelmet(item);

    // Add metadata to identify this as part of our warden
    armorStand.setMetadata("obsidian_warden_id", new FixedMetadataValue(plugin, warderId.toString()));

    return armorStand;
}
```

**Key Learning Points:**
- Armor stands become invisible "holders" for block items
- Items on armor stands create the visual appearance
- Metadata links all parts to the main entity
- Gravity and invulnerability prevent interference

## Step 4: Animation System (Entity Positioning)

### Synchronized Movement and Animation
```java
private void updateModelParts() {
    Location mainLoc = mainEntity.getLocation();
    Vector direction = mainLoc.getDirection();

    // Calculate animation values using time
    double time = animationTicks * 0.05;
    double armSwayAmount = 0.3;
    double shardBobAmount = 0.2;

    // Arm sway animation - arms move opposite to each other
    double leftArmSway = Math.sin(time) * armSwayAmount;
    double rightArmSway = Math.sin(time + Math.PI) * armSwayAmount;

    // Position arms relative to main entity
    Vector leftOffset = new Vector(-1.2, 0.5 + leftArmSway, 0)
        .rotateAroundY(Math.toRadians(mainLoc.getYaw()));
    leftArm.teleport(mainLoc.clone().add(leftOffset));

    // Floating shards with different bob patterns
    double shard1Bob = Math.sin(time * 1.2) * shardBobAmount;
    Vector shard1Offset = direction.clone().multiply(-1.5)
        .add(new Vector(-0.6, 1.5 + shard1Bob, 0));
    shard1.teleport(mainLoc.clone().add(shard1Offset));
}
```

**Key Learning Points:**
- `teleport()` moves armor stands to new positions every tick
- `Vector` math calculates relative positions
- `rotateAroundY()` keeps parts oriented with the main entity
- Sine waves create smooth, natural-looking movement

## Step 5: Multi-Block Appearance

### The Solution: Different Block Types
Instead of complex texture rendering, we use different vanilla blocks:

```java
// Create items for different model parts
private ItemStack createNetherBrickBlock() {
    ItemStack item = new ItemStack(Material.NETHER_BRICKS);
    ItemMeta meta = item.getItemMeta();
    meta.setDisplayName(ChatColor.DARK_RED + "Nether Brick Arm");
    item.setItemMeta(meta);
    return item;
}

private ItemStack createPurpleGlassBlock() {
    ItemStack item = new ItemStack(Material.PURPLE_STAINED_GLASS);
    ItemMeta meta = item.getItemMeta();
    meta.setDisplayName(ChatColor.LIGHT_PURPLE + "Floating Shard");
    item.setItemMeta(meta);
    return item;
}
```

### Block-to-Part Mapping
- **Body**: Obsidian helmet on main entity
- **Arms**: Nether brick blocks on armor stands
- **Horns**: Crying obsidian blocks on armor stands
- **Belt**: End stone block on armor stand
- **Shards**: Purple stained glass on armor stands

**Key Learning Points:**
- Each armor stand displays one block type
- Custom names help identify parts
- Vanilla blocks provide the visual theme
- No texture files or resource packs needed

## Step 6: Registration and Integration

### Server-Side Registration
```java
// Register entity type
public static final EntityType<ObsidianWardenEntity> OBSIDIAN_WARDEN = Registry.register(
    Registries.ENTITY_TYPE,
    new Identifier(MOD_ID, "obsidian_warden"),
    FabricEntityTypeBuilder.create(SpawnGroup.MONSTER, ObsidianWardenEntity::new)
        .dimensions(EntityDimensions.fixed(1.4f, 2.9f))
        .build()
);

// Register attributes
FabricDefaultAttributeRegistry.register(OBSIDIAN_WARDEN, ObsidianWardenEntity.createObsidianWardenAttributes());
```

### Client-Side Registration  
```java
// Register renderer
EntityRendererRegistry.register(ObsidianWardenMod.OBSIDIAN_WARDEN, ObsidianWardenEntityRenderer::new);

// Register model layer
EntityModelLayerRegistry.registerModelLayer(OBSIDIAN_WARDEN_LAYER, ObsidianWardenEntityModel::getTexturedModelData);
```

## Step 7: Testing Your Entity

### Using the Spawn Egg
1. Build the mod: `./gradlew build`
2. Install in Minecraft with Fabric
3. Creative mode → Spawn Eggs tab → "Obsidian Warden Spawn Egg"
4. Spawn and observe:
   - Model shape and proportions
   - Texture application on different parts
   - Idle animations (arm sway, shard bobbing)
   - Boss health bar
   - Combat behavior

### Debugging Tips
- Use `LOGGER.info()` to debug registration issues
- Check console for texture loading errors
- Verify model dimensions match entity hitbox
- Test animations by watching for several seconds

## Step 8: Customization Examples

### Easy Modifications
```java
// Make it bigger
matrices.scale(2.0f, 2.0f, 2.0f); // In renderer

// Change colors
model.shard1.render(matrices, vertexConsumer, light, overlay, 1.0f, 0.0f, 0.0f, 1.0f); // Red shards

// Faster animations  
this.leftArm.pitch = MathHelper.sin(ageInTicks * 0.1F) * armSwayAmount; // Double speed
```

### Advanced Extensions
- Add particle effects in `mobTick()`
- Implement ranged attacks with projectiles
- Create multiple model variants
- Add complex multi-part animations
- Implement phase-based boss behavior

## Key Takeaways

1. **No External Tools Required** - Everything is pure Java code
2. **Vanilla Texture Reuse** - No custom resource packs needed
3. **Modular Design** - Each component has a clear responsibility
4. **Beginner Friendly** - Extensive comments explain every concept
5. **Extensible** - Easy to modify and expand upon

This approach gives you complete control over your entity while keeping the barrier to entry low for beginners!
