# Obsidian Warden Plugin

A complete example of a Minecraft 1.21+ custom boss entity plugin that works on any server without requiring client-side mods.

## Features

### The Obsidian Warden Boss
- **Large obsidian body** - Main entity with obsidian armor and high stats
- **Chunky nether brick arms** - Animated armor stands with nether brick blocks
- **Crying obsidian head with horns** - Horn decorations using armor stands
- **Floating purple glass shards** - Mystical spines that bob and rotate
- **End stone belt** - Decorative waist piece

### Technical Features
- **Server-side only** - No client mods required, works on any server
- **Composite entity design** - Uses vanilla entities + armor stands for complex models
- **Vanilla block textures** - Reuses existing Minecraft blocks for appearance
- **Smooth animations** - Synchronized movement and floating effects
- **Boss mechanics** - Health bar, high stats, and enhanced AI
- **Plugin compatibility** - Works with Bukkit/Spigot/Paper servers

## How It Works

### Composite Entity Design
Instead of custom models, this plugin creates a "composite entity" using:

1. **Main Entity**: Wither <PERSON>keleton (provides AI, health, combat)
2. **Model Parts**: Armor stands with block items (visual components)
3. **Synchronization**: All parts move together as one entity

### Entity Components
```java
// Main entity - handles AI and combat
WitherSkeleton mainEntity = (WitherSkeleton) world.spawnEntity(location, EntityType.WITHER_SKELETON);

// Model parts - visual components
ArmorStand leftArm = createModelPart(location, "Left Arm", new ItemStack(Material.NETHER_BRICKS));
ArmorStand rightArm = createModelPart(location, "Right Arm", new ItemStack(Material.NETHER_BRICKS));
// ... more parts
```

### Animation System
Smooth animations using mathematical functions:

```java
// Arm sway animation
double leftArmSway = Math.sin(time) * armSwayAmount;
double rightArmSway = Math.sin(time + Math.PI) * armSwayAmount;

// Floating shard animation
double shard1Bob = Math.sin(time * 1.2) * shardBobAmount;
```

## Building and Testing

### Prerequisites
- Java 17+
- Maven 3.6+
- Bukkit/Spigot/Paper server 1.21.1+

### Build Instructions
1. Clone or download this project
2. Open terminal in the project directory
3. Run `mvn clean package`
4. The plugin JAR will be in `target/`

### Testing on Server
1. Install the plugin JAR in your server's `plugins` folder
2. Start/restart your server
3. Use `/obsidianwarden spawn` to create an Obsidian Warden
4. Or use `/obsidianwarden give` to get a spawn crystal

## Code Structure

```
src/main/java/com/example/obsidianwarden/
├── ObsidianWardenPlugin.java           # Main plugin class
├── entity/
│   └── ObsidianWardenEntity.java       # Composite entity logic
├── commands/
│   └── ObsidianWardenCommand.java      # Command handling
└── listeners/
    └── EntityListener.java             # Event handling
```

## Commands

### Admin Commands
- `/obsidianwarden spawn` - Spawn an Obsidian Warden at your location
- `/obsidianwarden give` - Get an Obsidian Warden spawn crystal
- `/obsidianwarden list` - List all active Obsidian Wardens
- `/obsidianwarden cleanup` - Remove all Obsidian Wardens
- `/obsidianwarden help` - Show command help

### Permissions
- `obsidianwarden.spawn` - Allow spawning Obsidian Wardens
- `obsidianwarden.admin` - Full admin access to all commands

## Learning Points

### For Beginners
1. **Plugin Development** - Basic Bukkit/Spigot plugin structure
2. **Entity Manipulation** - Working with vanilla entities
3. **Composite Design** - Combining multiple entities into one
4. **Event Handling** - Listening to and modifying game events
5. **Command Systems** - Creating custom commands with permissions

### Advanced Concepts
1. **Entity Synchronization** - Keeping multiple entities moving together
2. **Metadata Usage** - Tagging entities for identification
3. **Animation Mathematics** - Using trigonometry for smooth movement
4. **Boss Bar Management** - Dynamic health display for players
5. **Performance Optimization** - Efficient update loops and cleanup

## Customization Ideas

### Easy Modifications
- Change entity stats in `setupMainEntity()`
- Adjust animation speeds and patterns in `updateModelParts()`
- Modify model part positions and offsets
- Change block types used for different parts
- Add new model parts (more arms, decorations, etc.)

### Advanced Extensions
- Add particle effects during animations
- Implement special attack patterns
- Create multiple boss variants
- Add custom sound effects
- Implement phase-based boss behavior
- Add custom drops and rewards

## Server Compatibility

This plugin works on any server without requiring client-side modifications:
- **Bukkit/Spigot/Paper** - Full compatibility
- **No client mods needed** - Players see everything with vanilla client
- **Cross-version support** - Works on most modern Minecraft versions
- **Lightweight** - Minimal performance impact

The "model" is created entirely through server-side entity positioning and vanilla block items, making it universally compatible!
